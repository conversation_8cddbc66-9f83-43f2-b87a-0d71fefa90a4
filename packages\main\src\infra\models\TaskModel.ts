import { Task } from '@app/shared/types/database.types.js'

/**
 * 上传任务状态枚举
 */
export enum UploadTaskStatus {
  PENDING = 0,      // 等待上传
  UPLOADING = 1,    // 上传中
  PAUSED = 2,       // 已暂停
  COMPLETED = 3,    // 上传完成
  FAILED = 4,       // 上传失败
  CANCELLED = 5     // 已取消
}

/**
 * 上传任务类型枚举
 */
export enum UploadTaskType {
  VIDEO = 1,        // 视频文件
  AUDIO = 2,        // 音频文件
  IMAGE = 3,        // 图片文件
  OTHER = 4         // 其他文件
}

/**
 * 上传任务领域模型类
 * 专门用于管理文件上传任务
 */
export class UploadTaskModel {

  /**
   * 任务ID
   */
  id: number

  /**
   * 团队ID
   */
  team_id: number

  /**
   * 用户ID
   */
  uid: string

  /**
   * 任务名称（文件名）
   */
  name: string

  /**
   * 本地文件路径
   */
  local_path: string

  /**
   * 云端URL（上传完成后）
   */
  url: string

  /**
   * 文件哈希值
   */
  hash: string

  /**
   * 文件大小（字节）
   */
  size: number



  /**
   * 上传进度（0-100）
   */
  progress: number

  /**
   * 任务状态
   */
  status: UploadTaskStatus

  /**
   * 任务类型
   */
  type: UploadTaskType

  /**
   * 失败原因
   */
  reason: string

  /**
   * 所属文件夹ID
   */
  folder_id: string

  /**
   * OSS对象键
   */
  object_key: string

  /**
   * OSS对象ID
   */
  object_id: string

  /**
   * 上传模块类型
   */
  upload_module: string

  /**
   * OSS断点续传数据（JSON格式）
   */
  checkpoint_data: string

  /**
   * 删除时间戳
   */
  deleted_at: number

  /**
   * 更新时间戳
   */
  updated_at: number

  /**
   * 创建时间戳
   */
  created_at: number

  /**
   * 构造函数
   * @param data 上传任务数据
   */
  constructor(data: Partial<UploadTaskModel> = {}) {
    this.id = data.id ?? 0
    this.team_id = data.team_id ?? 0
    this.uid = data.uid ?? ''
    this.name = data.name ?? ''
    this.local_path = data.local_path ?? ''
    this.url = data.url ?? ''
    this.hash = data.hash ?? ''
    this.size = data.size ?? 0

    this.progress = data.progress ?? 0
    this.status = data.status ?? UploadTaskStatus.PENDING
    this.type = data.type ?? UploadTaskType.OTHER
    this.reason = data.reason ?? ''
    this.folder_id = data.folder_id ?? ''
    this.object_key = data.object_key ?? ''
    this.object_id = data.object_id ?? ''
    this.upload_module = data.upload_module ?? 'media'
    this.checkpoint_data = data.checkpoint_data ?? ''
    this.deleted_at = data.deleted_at ?? 0
    this.updated_at = data.updated_at ?? Date.now()
    this.created_at = data.created_at ?? Date.now()
  }

  /**
   * 判断是否已删除
   */
  isDeleted(): boolean {
    return this.deleted_at > 0
  }

  /**
   * 判断是否属于指定用户
   * @param uid 用户ID
   */
  belongsToUser(uid: string): boolean {
    return this.uid === uid
  }

  /**
   * 判断是否属于指定团队
   * @param teamId 团队ID
   */
  belongsToTeam(teamId: number): boolean {
    return this.team_id === teamId
  }

  /**
   * 判断任务是否处于等待状态
   */
  isPending(): boolean {
    return this.status === UploadTaskStatus.PENDING
  }

  /**
   * 判断任务是否正在上传
   */
  isUploading(): boolean {
    return this.status === UploadTaskStatus.UPLOADING
  }

  /**
   * 判断任务是否已暂停
   */
  isPaused(): boolean {
    return this.status === UploadTaskStatus.PAUSED
  }

  /**
   * 判断任务是否已完成
   */
  isCompleted(): boolean {
    return this.status === UploadTaskStatus.COMPLETED
  }

  /**
   * 判断任务是否失败
   */
  isFailed(): boolean {
    return this.status === UploadTaskStatus.FAILED
  }

  /**
   * 判断任务是否已取消
   */
  isCancelled(): boolean {
    return this.status === UploadTaskStatus.CANCELLED
  }

  /**
   * 判断任务是否可以暂停
   */
  canPause(): boolean {
    return this.status === UploadTaskStatus.UPLOADING
  }

  /**
   * 判断任务是否可以恢复
   */
  canResume(): boolean {
    return this.status === UploadTaskStatus.PAUSED || this.status === UploadTaskStatus.FAILED
  }

  /**
   * 判断任务是否可以取消
   */
  canCancel(): boolean {
    return this.status === UploadTaskStatus.PENDING ||
           this.status === UploadTaskStatus.UPLOADING ||
           this.status === UploadTaskStatus.PAUSED
  }

  /**
   * 判断任务是否可以重试
   */
  canRetry(): boolean {
    return this.status === UploadTaskStatus.FAILED
  }

  /**
   * 获取任务的显示名称
   */
  getDisplayName(): string {
    return this.name || '未命名文件'
  }

  /**
   * 获取任务状态文本
   */
  getStatusText(): string {
    const statusMap = {
      [UploadTaskStatus.PENDING]: '等待上传',
      [UploadTaskStatus.UPLOADING]: '上传中',
      [UploadTaskStatus.PAUSED]: '已暂停',
      [UploadTaskStatus.COMPLETED]: '上传完成',
      [UploadTaskStatus.FAILED]: '上传失败',
      [UploadTaskStatus.CANCELLED]: '已取消'
    }
    return statusMap[this.status] || '未知状态'
  }

  /**
   * 获取任务类型文本
   */
  getTypeText(): string {
    const typeMap = {
      [UploadTaskType.VIDEO]: '视频文件',
      [UploadTaskType.AUDIO]: '音频文件',
      [UploadTaskType.IMAGE]: '图片文件',
      [UploadTaskType.OTHER]: '其他文件'
    }
    return typeMap[this.type] || '未知类型'
  }

  /**
   * 获取已上传字节数（通过小数进度计算）
   */
  getUploadedBytes(): number {
    return Math.round(this.progress * this.size)
  }

  /**
   * 获取任务进度百分比字符串
   */
  getProgressText(): string {
    return `${(this.progress * 100).toFixed(1)}%`
  }

  /**
   * 获取上传速度文本
   */
  getUploadSpeedText(timeElapsed: number): string {
    const uploadedBytes = this.getUploadedBytes()
    if (timeElapsed <= 0 || uploadedBytes <= 0) return '0 B/s'

    const speed = uploadedBytes / timeElapsed // bytes per second
    return this.formatBytes(speed) + '/s'
  }

  /**
   * 获取剩余时间估算
   */
  getEstimatedTimeRemaining(timeElapsed: number): string {
    const uploadedBytes = this.getUploadedBytes()
    if (timeElapsed <= 0 || uploadedBytes <= 0 || this.progress >= 1) {
      return '--'
    }

    const speed = uploadedBytes / timeElapsed
    const remainingBytes = this.size - uploadedBytes
    const remainingSeconds = remainingBytes / speed

    if (remainingSeconds < 60) {
      return `${Math.ceil(remainingSeconds)}秒`
    } else if (remainingSeconds < 3600) {
      return `${Math.ceil(remainingSeconds / 60)}分钟`
    } else {
      return `${Math.ceil(remainingSeconds / 3600)}小时`
    }
  }

  /**
   * 获取格式化的文件大小
   */
  getFormattedSize(): string {
    return this.formatBytes(this.size)
  }

  /**
   * 获取已上传大小的格式化文本
   */
  getFormattedUploadedSize(): string {
    return this.formatBytes(this.getUploadedBytes())
  }

  /**
   * 格式化字节数
   */
  private formatBytes(bytes: number): string {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    if (bytes === 0) return '0 B'

    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    const size = parseFloat((bytes / Math.pow(1024, i)).toFixed(2))

    return `${size} ${sizes[i]}`
  }

  /**
   * 更新上传进度
   */
  updateProgress(progress: number): void {
    // 直接使用小数进度值（0-1）
    this.progress = progress
    this.updated_at = Date.now()
  }

  /**
   * 设置任务状态
   */
  setStatus(status: UploadTaskStatus, reason?: string): void {
    this.status = status
    if (reason) {
      this.reason = reason
    }
    this.updated_at = Date.now()
  }

  /**
   * 重置任务（用于重试）
   */
  reset(): void {
    this.progress = 0
    this.status = UploadTaskStatus.PENDING
    this.reason = ''
    this.checkpoint_data = '' // 清空断点数据
    this.updated_at = Date.now()
  }

  /**
   * 将上传任务转换为JSON对象
   */
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      team_id: this.team_id,
      uid: this.uid,
      name: this.name,
      local_path: this.local_path,
      url: this.url,
      hash: this.hash,
      size: this.size,
      uploaded_bytes: this.getUploadedBytes(), // 为了向后兼容保留此字段
      progress: this.progress,
      status: this.status,
      type: this.type,
      reason: this.reason,
      folder_id: this.folder_id,
      object_key: this.object_key,
      object_id: this.object_id,
      upload_module: this.upload_module,
      checkpoint_data: this.checkpoint_data,
      deleted_at: this.deleted_at,
      updated_at: this.updated_at,
      created_at: this.created_at
    }
  }
}

export class TaskModel implements Task.ITask {

  id: number
  team_id: number
  group_id: number
  make_cover_frame: number
  make_track_frame: number
  need_bind: number
  path: string
  uid: string
  url: string
  script_id: number
  resource_id: string
  cover: string
  cover_frame: string
  track_frame: string
  name: string
  codec_name: string
  hash: string
  duration: number
  width: number
  height: number
  size: number
  status: number
  progress: number
  folder_id: string
  upload_id: string
  type: number
  reason: string
  task_nos: string
  extra_tasks: string
  upload_type: string
  deleted_at: number
  updated_at: number
  created_at: number
  tag_id: number
  object_key: string
  platform: number

  constructor(data: Partial<TaskModel> = {}) {
    this.id = data.id ?? 0
    this.team_id = data.team_id ?? 0
    this.group_id = data.group_id ?? 0
    this.make_cover_frame = data.make_cover_frame ?? 0
    this.make_track_frame = data.make_track_frame ?? 0
    this.need_bind = data.need_bind ?? 0
    this.path = data.path ?? ''
    this.uid = data.uid ?? ''
    this.url = data.url ?? ''
    this.script_id = data.script_id ?? 0
    this.resource_id = data.resource_id ?? ''
    this.cover = data.cover ?? ''
    this.cover_frame = data.cover_frame ?? ''
    this.track_frame = data.track_frame ?? ''
    this.name = data.name ?? ''
    this.codec_name = data.codec_name ?? ''
    this.hash = data.hash ?? ''
    this.duration = data.duration ?? 0
    this.width = data.width ?? 0
    this.height = data.height ?? 0
    this.size = data.size ?? 0
    this.status = data.status ?? 0
    this.progress = data.progress ?? 0
    this.folder_id = data.folder_id ?? ''
    this.upload_id = data.upload_id ?? ''
    this.type = data.type ?? 0
    this.reason = data.reason ?? ''
    this.task_nos = data.task_nos ?? ''
    this.extra_tasks = data.extra_tasks ?? ''
    this.upload_type = data.upload_type ?? ''
    this.deleted_at = data.deleted_at ?? 0
    this.updated_at = data.updated_at ?? Date.now()
    this.created_at = data.created_at ?? Date.now()
    this.tag_id = data.tag_id ?? 0
    this.object_key = data.object_key ?? ''
    this.platform = data.platform ?? Task.Platform.DESKTOP
  }

  isDeleted(): boolean {
    return this.deleted_at > 0
  }

  belongsToUser(uid: string): boolean {
    return this.uid === uid
  }

  belongsToTeam(teamId: number): boolean {
    return this.team_id === teamId
  }

  isPending(): boolean {
    return this.status === Task.Status.PENDING
  }

  isProcessing(): boolean {
    return this.status === Task.Status.PROCESSING
  }

  isCompleted(): boolean {
    return this.status === Task.Status.COMPLETED
  }

  isFailed(): boolean {
    return this.status === Task.Status.FAILED
  }

  isCanceled(): boolean {
    return this.status === Task.Status.CANCELED
  }

  getDisplayName(): string {
    return this.name || '未命名任务'
  }

  getProgressText(): string {
    return `${this.progress}%`
  }

  getFormattedDuration(): string {
    const totalSeconds = this.duration
    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = Math.floor(totalSeconds % 60)

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    }
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }

  getFormattedSize(): string {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    if (this.size === 0) return '0 B'

    const i = Math.floor(Math.log(this.size) / Math.log(1024))
    const size = parseFloat((this.size / Math.pow(1024, i)).toFixed(2))

    return `${size} ${sizes[i]}`
  }

  getResolution(): string {
    if (this.width === 0 || this.height === 0) return ''
    return `${this.width}x${this.height}`
  }

  toJSON(): Record<string, any> {
    return {
      id: this.id,
      team_id: this.team_id,
      group_id: this.group_id,
      make_cover_frame: this.make_cover_frame,
      make_track_frame: this.make_track_frame,
      need_bind: this.need_bind,
      path: this.path,
      uid: this.uid,
      url: this.url,
      script_id: this.script_id,
      resource_id: this.resource_id,
      cover: this.cover,
      cover_frame: this.cover_frame,
      track_frame: this.track_frame,
      name: this.name,
      codec_name: this.codec_name,
      hash: this.hash,
      duration: this.duration,
      width: this.width,
      height: this.height,
      size: this.size,
      status: this.status,
      progress: this.progress,
      folder_id: this.folder_id,
      upload_id: this.upload_id,
      type: this.type,
      reason: this.reason,
      task_nos: this.task_nos,
      extra_tasks: this.extra_tasks,
      upload_type: this.upload_type,
      deleted_at: this.deleted_at,
      updated_at: this.updated_at,
      created_at: this.created_at,
      tag_id: this.tag_id,
      object_key: this.object_key,
      platform: this.platform
    }
  }
}

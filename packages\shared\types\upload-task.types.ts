import { BaseEntity, OwnershipFields } from './database.types.js'

/**
 * 上传任务状态枚举
 */
export enum UploadTaskStatus {
  PENDING = 0,      // 等待上传
  UPLOADING = 1,    // 上传中
  PAUSED = 2,       // 已暂停
  COMPLETED = 3,    // 上传完成
  FAILED = 4,       // 上传失败
  CANCELLED = 5     // 已取消
}

/**
 * 上传任务类型枚举
 */
export enum UploadTaskType {
  VIDEO = 1,        // 视频文件
  AUDIO = 2,        // 音频文件
  IMAGE = 3,        // 图片文件
  OTHER = 4         // 其他文件
}

/**
 * 上传任务基础接口
 */
export interface IUploadTask extends BaseEntity, OwnershipFields {
  id: number
  team_id: number
  name: string
  local_path: string
  url: string
  hash: string
  size: number
  progress: number
  status: UploadTaskStatus
  type: UploadTaskType
  reason: string
  folder_id: string
  object_key: string
  object_id: string
  upload_module: string
  checkpoint_data: string
}

/**
 * 创建上传任务参数
 */
export interface CreateUploadTaskParams extends OwnershipFields {
  name: string
  local_path: string
  type?: UploadTaskType
  folder_id?: string
  upload_module?: string
}

/**
 * 更新上传任务参数
 */
export interface UpdateUploadTaskParams {
  name?: string
  url?: string
  hash?: string
  progress?: number
  status?: UploadTaskStatus
  reason?: string
  cover?: string
  object_key?: string
  object_id?: string
  resume_data?: string
  checkpoint_data?: string
  size?: number
  local_path?: string
}

/**
 * 查询上传任务参数
 */
export interface QueryUploadTaskParams extends Partial<OwnershipFields> {
  status?: UploadTaskStatus | UploadTaskStatus[]
  type?: UploadTaskType | UploadTaskType[]
  folder_id?: string
  keyword?: string
  start_date?: number
  end_date?: number
}

/**
 * 上传任务统计结果
 */
export interface UploadTaskStatsResult {
  total_count: number
  pending_count: number
  uploading_count: number
  paused_count: number
  completed_count: number
  failed_count: number
  cancelled_count: number
  total_size: number
  uploaded_size: number
  type_distribution: Record<UploadTaskType, number>
}

/**
 * 批量操作参数
 */
export interface BatchUploadTaskParams {
  ids: number[]
  action: 'pause' | 'resume' | 'cancel' | 'retry' | 'delete'
}

/**
 * 上传进度事件数据
 */
export interface UploadProgressEvent {
  task_id: number
  progress: number
  speed?: number
  estimated_time?: number
}

/**
 * 上传任务队列配置
 */
export interface UploadQueueConfig {
  max_concurrent_uploads: number
  retry_attempts: number
  retry_delay: number
  chunk_size: number
}

/**
 * 断点续传数据
 */
export interface ResumeUploadData {
  upload_id?: string
  part_number?: number
  etag?: string
  checkpoint?: any
}

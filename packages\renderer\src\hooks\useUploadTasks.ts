import { useEffect, useCallback } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { TokenManager, TeamManager } from '@/libs/storage'
import {
  IUploadTask,
  UploadTaskStatus,
  BatchUploadTaskParams,
  UploadProgressEvent
} from '@app/shared/types/upload-task.types'

/**
 * 上传任务管理 Hook
 */
export function useUploadTasks() {
  const queryClient = useQueryClient()
  const currentUser = TokenManager.getUserId()
  const currentTeam = TeamManager.current()

  // 获取用户上传任务
  const { data: tasks = [], isLoading, refetch } = useQuery({
    queryKey: ['uploadTasks', currentUser, currentTeam],
    queryFn: async () => {
      if (!currentUser) return []
      return window.uploadTask.userTasks({
        uid: String(currentUser),
        teamId: currentTeam
      })
    },
    enabled: !!currentUser,
    refetchInterval: 5000 // 每5秒刷新一次
  })

  // 获取上传统计
  const { data: stats } = useQuery({
    queryKey: ['uploadTaskStats', currentUser, currentTeam],
    queryFn: async () => {
      if (!currentUser) return null
      return window.uploadTask.getStats({
        uid: String(currentUser),
        teamId: currentTeam
      })
    },
    enabled: !!currentUser,
    refetchInterval: 10000 // 每10秒刷新一次
  })

  // 获取队列状态
  const { data: queueStatus } = useQuery({
    queryKey: ['uploadQueueStatus'],
    queryFn: () => window.uploadTask.getQueueStatus(),
    refetchInterval: 3000 // 每3秒刷新一次
  })

  // 创建上传任务
  const createTaskMutation = useMutation({
    mutationFn: async (params: any) => {
      return window.uploadTask.create(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
    }
  })

  // 开始上传
  const startUploadMutation = useMutation({
    mutationFn: async (taskId: number) => {
      return window.uploadTask.startUpload({ id: taskId })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    }
  })

  // 暂停上传
  const pauseUploadMutation = useMutation({
    mutationFn: async (taskId: number) => {
      return window.uploadTask.pauseUpload({ id: taskId })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    }
  })

  // 恢复上传
  const resumeUploadMutation = useMutation({
    mutationFn: async (taskId: number) => {
      return window.uploadTask.resumeUpload({ id: taskId })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    }
  })

  // 取消上传
  const cancelUploadMutation = useMutation({
    mutationFn: async (taskId: number) => {
      return window.uploadTask.cancelUpload({ id: taskId })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    }
  })

  // 重试上传
  const retryUploadMutation = useMutation({
    mutationFn: async (taskId: number) => {
      return window.uploadTask.retryUpload({ id: taskId })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    }
  })

  // 批量操作
  const batchOperationMutation = useMutation({
    mutationFn: async (params: BatchUploadTaskParams) => {
      return window.uploadTask.batchOperation(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    }
  })

  // 删除任务
  const deleteTaskMutation = useMutation({
    mutationFn: async (taskId: number) => {
      return window.uploadTask.delete(taskId)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
    }
  })

  // 清理已完成任务
  const cleanupCompletedMutation = useMutation({
    mutationFn: async (olderThanDays?: number) => {
      if (!currentUser) return 0
      return window.uploadTask.cleanupCompleted({
        uid: String(currentUser),
        teamId: currentTeam,
        olderThanDays
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
    }
  })

  // 上传文件内容
  const uploadFileContentMutation = useMutation({
    mutationFn: async (params: {
      taskId: number
      fileContent: ArrayBuffer
      fileName: string
    }) => {
      return window.uploadTask.uploadFileContent(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
    }
  })

  // 从路径上传文件
  const uploadFromPathMutation = useMutation({
    mutationFn: async (params: {
      taskId: number
      filePath: string
    }) => {
      return window.uploadTask.uploadFromPath(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
    }
  })

  // 监听上传进度事件
  useEffect(() => {
    const handleProgressEvent = (_event: any, data: UploadProgressEvent) => {
      console.log(`[useUploadTasks] 接收到进度事件: 任务${data.task_id}, 进度${(data.progress).toFixed(2)}%`)
      // 更新特定任务的进度
      queryClient.setQueryData(['uploadTasks', currentUser, currentTeam], (oldTasks: IUploadTask[] | undefined) => {
        if (!oldTasks) return oldTasks

        return oldTasks.map(task =>
          task.id === data.task_id
            ? {
              ...task,
              progress: data.progress
            }
            : task
        )
      })
    }

    // 监听进度事件
    window.electronAPI?.ipcRenderer.on('upload-task-progress', handleProgressEvent)

    return () => {
      window.electronAPI?.ipcRenderer.removeListener('upload-task-progress', handleProgressEvent)
    }
  }, [currentUser, currentTeam])

  // 便捷方法
  const getTasksByStatus = useCallback((status: UploadTaskStatus) => {
    return tasks.filter(task => task.status === status)
  }, [tasks])

  const getPendingTasks = useCallback(() => getTasksByStatus(UploadTaskStatus.PENDING), [getTasksByStatus])
  const getUploadingTasks = useCallback(() => getTasksByStatus(UploadTaskStatus.UPLOADING), [getTasksByStatus])
  const getPausedTasks = useCallback(() => getTasksByStatus(UploadTaskStatus.PAUSED), [getTasksByStatus])
  const getCompletedTasks = useCallback(() => getTasksByStatus(UploadTaskStatus.COMPLETED), [getTasksByStatus])
  const getFailedTasks = useCallback(() => getTasksByStatus(UploadTaskStatus.FAILED), [getTasksByStatus])

  return {
    // 数据
    tasks,
    stats,
    queueStatus,
    isLoading,

    // 查询方法
    refetch,
    getTasksByStatus,
    getPendingTasks,
    getUploadingTasks,
    getPausedTasks,
    getCompletedTasks,
    getFailedTasks,

    // 操作方法
    createTask: createTaskMutation.mutateAsync,
    startUpload: startUploadMutation.mutateAsync,
    pauseUpload: pauseUploadMutation.mutateAsync,
    resumeUpload: resumeUploadMutation.mutateAsync,
    cancelUpload: cancelUploadMutation.mutateAsync,
    retryUpload: retryUploadMutation.mutateAsync,
    batchOperation: batchOperationMutation.mutateAsync,
    deleteTask: deleteTaskMutation.mutateAsync,
    cleanupCompleted: cleanupCompletedMutation.mutateAsync,
    uploadFileContent: uploadFileContentMutation.mutateAsync,
    uploadFromPath: uploadFromPathMutation.mutateAsync,

    // 加载状态
    isCreating: createTaskMutation.isPending,
    isOperating: startUploadMutation.isPending ||
                 pauseUploadMutation.isPending ||
                 resumeUploadMutation.isPending ||
                 cancelUploadMutation.isPending ||
                 retryUploadMutation.isPending,
    isBatchOperating: batchOperationMutation.isPending,
    isDeleting: deleteTaskMutation.isPending,
    isCleaning: cleanupCompletedMutation.isPending,
    isUploading: uploadFileContentMutation.isPending || uploadFromPathMutation.isPending
  }
}

import React, { useState, useCallback } from 'react'
import { useUploadTasks } from '@/hooks/useUploadTasks'
import { UploadTaskManager } from './UploadTaskManager'
import { TokenManager, TeamManager } from '@/libs/storage'
import { UploadTaskType } from '@app/shared/types/upload-task.types'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Upload, FileText, X } from 'lucide-react'
import { UploadModule } from '@app/shared/types/ipc/file-uploader'

/**
 * 上传任务使用示例组件
 */
export const UploadTaskExample: React.FC = () => {
  const [selectedFilePaths, setSelectedFilePaths] = useState<string[]>([])
  const [isCreatingTasks, setIsCreatingTasks] = useState(false)

  const { createTask } = useUploadTasks()

  // 获取当前用户信息
  const currentUser = TokenManager.getUserId()
  const currentTeam = TeamManager.current()

  // 选择文件
  const handleSelectFiles = useCallback(async () => {
    try {
      const filePaths = await window.uploadTask.selectFiles({
        multiple: true,
        filters: [
          { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff'] },
        ]
      })

      if (filePaths.length > 0) {
        setSelectedFilePaths(filePaths)
        console.log('选择的文件:', filePaths)
      }
    } catch (error) {
      console.error('选择文件失败:', error)
    }
  }, [])

  // 清空选择的文件
  const handleClearFiles = useCallback(() => {
    setSelectedFilePaths([])
  }, [])

  // 获取文件类型
  const getFileType = (fileName: string): UploadTaskType => {
    const ext = fileName.toLowerCase().split('.').pop() || ''
    
    const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v']
    const audioExts = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a']
    const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff']
    
    if (videoExts.includes(ext)) return UploadTaskType.VIDEO
    if (audioExts.includes(ext)) return UploadTaskType.AUDIO
    if (imageExts.includes(ext)) return UploadTaskType.IMAGE
    
    return UploadTaskType.OTHER
  }

  // 创建上传任务并开始上传
  const handleCreateTasks = useCallback(async () => {
    if (!selectedFilePaths.length || !currentUser) {
      console.error('没有选择文件或用户未登录')
      return
    }

    setIsCreatingTasks(true)

    try {
      const tasks: any[] = []

      for (const filePath of selectedFilePaths) {
        console.log('处理文件:', filePath)

        const fileName = filePath.split(/[/\\]/).pop() || 'unknown'

        // 创建上传任务
        const task = await createTask({
          uid: String(currentUser),
          team_id: currentTeam || undefined,
          name: fileName,
          local_path: filePath,
          type: getFileType(fileName),
          upload_module: UploadModule.publishing
        })

        tasks.push(task)

        // 立即开始上传文件
        try {
          console.log(`开始上传文件: ${fileName}`)

          // 调用从路径上传的方法
          const uploadResult = await window.uploadTask.uploadFromPath({
            taskId: task.id,
            filePath: filePath
          })

          if (uploadResult.success) {
            console.log(`文件 ${fileName} 上传成功:`, uploadResult.url)
          } else {
            console.error(`文件 ${fileName} 上传失败:`, uploadResult.error)
          }
        } catch (uploadError) {
          console.error(`上传文件 ${fileName} 时出错:`, uploadError)
        }
      }

      console.log(`成功创建并开始上传 ${tasks.length} 个任务`)

      // 清空选择的文件
      setSelectedFilePaths([])
    } catch (error) {
      console.error('创建上传任务失败:', error)
    } finally {
      setIsCreatingTasks(false)
    }
  }, [selectedFilePaths, currentUser, currentTeam, createTask])

  return (
    <div className="space-y-6">
      {/* 文件选择区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5" />
            创建上传任务
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 文件选择 */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleSelectFiles}
              className="flex-1 flex items-center gap-2"
            >
              <Upload className="w-4 h-4" />
              选择文件
            </Button>
            <Button
              variant="outline"
              onClick={handleClearFiles}
              disabled={selectedFilePaths.length === 0}
              className="flex items-center gap-2"
            >
              <X className="w-4 h-4" />
              清空
            </Button>
          </div>

          {/* 选中的文件列表 */}
          {selectedFilePaths.length > 0 && (
            <div className="space-y-2">
              <div className="text-sm font-medium">
                已选择 {selectedFilePaths.length} 个文件:
              </div>
              <div className="max-h-40 overflow-y-auto space-y-1">
                {selectedFilePaths.map((filePath, index) => {
                  const fileName = filePath.split(/[/\\]/).pop() || 'unknown'
                  return (
                    <div key={index} className="flex items-center justify-between p-2  rounded text-sm">
                      <span className="truncate flex-1" title={filePath}>{fileName}</span>
                      <FileText className="w-4 h-4 text-gray-400 ml-2" />
                    </div>
                  )
                })}
              </div>
            </div>
          )}

          {/* 创建任务按钮 */}
          <Button
            onClick={handleCreateTasks}
            disabled={selectedFilePaths.length === 0 || isCreatingTasks || !currentUser}
            className="w-full flex items-center gap-2"
          >
            <Upload className="w-4 h-4" />
            {isCreatingTasks ? '创建并上传中...' : '创建上传任务'}
          </Button>

          {/* 用户状态提示 */}
          {!currentUser && (
            <div className="text-sm text-red-600  p-2 rounded">
              请先登录后再创建上传任务
            </div>
          )}
        </CardContent>
      </Card>

      {/* 上传任务管理器 */}
      <Card>
        <CardHeader>
          <CardTitle>上传任务管理</CardTitle>
        </CardHeader>
        <CardContent>
          <UploadTaskManager />
        </CardContent>
      </Card>
 
    </div>
  )
}

import { Inject, Injectable } from '@nestjs/common'
import { CrudableBaseIPCHandler, IPCHandlerError } from '@/infra/types/CrudableBaseIPCHandler.js'
import { UploadTaskService } from './upload-task.service.js'
import { BatchUploadTaskParams } from '@app/shared/types/upload-task.types.js'

/**
 * 上传任务 IPC 处理器
 */
@Injectable()
export class UploadTaskIPCHandler extends CrudableBaseIPCHandler<'uploadTask'> {

  constructor(
    @Inject(UploadTaskService)
    readonly uploadTaskService: UploadTaskService,
  ) {
    super(uploadTaskService, 'uploadTask')
  }

  /**
   * 注册额外的 IPC 处理程序
   */
  protected registerExtraHandlers(): void {
    // 获取用户上传任务
    this.registerHandler(
      'userTasks',
      async data => {
        if (!data) {
          throw new IPCHandlerError('获取用户上传任务参数不能为空')
        }
        if (!data.uid) {
          throw new IPCHandlerError('用户ID不能为空')
        }
        return this.uploadTaskService.getUserTasks(data.uid, data.status, data.teamId)
      }
    )

    // 获取文件夹下的上传任务
    this.registerHandler(
      'folderTasks',
      async data => {
        if (!data) {
          throw new IPCHandlerError('获取文件夹上传任务参数不能为空')
        }
        if (!data.folderId) {
          throw new IPCHandlerError('文件夹ID不能为空')
        }
        if (!data.uid) {
          throw new IPCHandlerError('用户ID不能为空')
        }
        return this.uploadTaskService.getTasksByFolder(data.folderId, data.uid, data.teamId)
      }
    )

    // 搜索上传任务
    this.registerHandler(
      'search',
      async data => {
        if (!data) {
          throw new IPCHandlerError('搜索上传任务参数不能为空')
        }
        if (!data.keyword) {
          throw new IPCHandlerError('搜索关键词不能为空')
        }
        if (!data.uid) {
          throw new IPCHandlerError('用户ID不能为空')
        }
        return this.uploadTaskService.searchTasks(data.keyword, data.uid, data.teamId)
      }
    )

    // 获取上传任务统计
    this.registerHandler(
      'getStats',
      async data => {
        if (!data) {
          throw new IPCHandlerError('获取上传任务统计参数不能为空')
        }
        if (!data.uid) {
          throw new IPCHandlerError('用户ID不能为空')
        }
        return this.uploadTaskService.getTaskStats(data.uid, data.teamId)
      }
    )

    // 开始上传任务
    this.registerHandler(
      'startUpload',
      async data => {
        if (!data) {
          throw new IPCHandlerError('开始上传任务参数不能为空')
        }
        if (data.id === undefined || data.id === null) {
          throw new IPCHandlerError('任务ID不能为空')
        }
        // 这里只是更新状态，实际上传逻辑在队列管理器中处理
        return this.uploadTaskService.updateTaskStatus(data.id, 1) // UPLOADING
      }
    )

    // 暂停上传任务
    this.registerHandler(
      'pauseUpload',
      async data => {
        if (!data) {
          throw new IPCHandlerError('暂停上传任务参数不能为空')
        }
        if (data.id === undefined || data.id === null) {
          throw new IPCHandlerError('任务ID不能为空')
        }
        return this.uploadTaskService.pauseTask(data.id)
      }
    )

    // 恢复上传任务
    this.registerHandler(
      'resumeUpload',
      async data => {
        if (!data) {
          throw new IPCHandlerError('恢复上传任务参数不能为空')
        }
        if (data.id === undefined || data.id === null) {
          throw new IPCHandlerError('任务ID不能为空')
        }
        return this.uploadTaskService.resumeTask(data.id)
      }
    )

    // 取消上传任务
    this.registerHandler(
      'cancelUpload',
      async data => {
        if (!data) {
          throw new IPCHandlerError('取消上传任务参数不能为空')
        }
        if (data.id === undefined || data.id === null) {
          throw new IPCHandlerError('任务ID不能为空')
        }
        return this.uploadTaskService.cancelTask(data.id)
      }
    )

    // 重试上传任务
    this.registerHandler(
      'retryUpload',
      async data => {
        if (!data) {
          throw new IPCHandlerError('重试上传任务参数不能为空')
        }
        if (data.id === undefined || data.id === null) {
          throw new IPCHandlerError('任务ID不能为空')
        }
        return this.uploadTaskService.retryTask(data.id)
      }
    )

    // 批量操作上传任务
    this.registerHandler(
      'batchOperation',
      async (data: BatchUploadTaskParams) => {
        if (!data) {
          throw new IPCHandlerError('批量操作参数不能为空')
        }
        if (!data.ids || !Array.isArray(data.ids) || data.ids.length === 0) {
          throw new IPCHandlerError('任务ID数组不能为空')
        }
        if (!data.action) {
          throw new IPCHandlerError('操作类型不能为空')
        }

        switch (data.action) {
          case 'pause': {
            return this.uploadTaskService.batchUpdateStatus(data.ids, 2) // PAUSED
          }
          case 'resume': {
            return this.uploadTaskService.batchUpdateStatus(data.ids, 0) // PENDING
          }
          case 'cancel': {
            return this.uploadTaskService.batchUpdateStatus(data.ids, 5, '用户取消') // CANCELLED
          }
          case 'retry': {
            // 重试需要逐个处理，因为需要重置任务状态
            let retryCount = 0
            for (const id of data.ids) {
              try {
                if (this.uploadTaskService.retryTask(id)) {
                  retryCount++
                }
              } catch (error) {
                // 忽略单个任务重试失败
              }
            }
            return retryCount
          }
          case 'delete': {
            return this.uploadTaskService.batchDelete(data.ids)
          }
          default: {
            throw new IPCHandlerError(`不支持的操作类型: ${data.action}`)
          }
        }
      }
    )

    // 获取上传队列配置
    this.registerHandler(
      'getQueueConfig',
      async () => {
        return this.uploadTaskService.getQueueConfig()
      }
    )

    // 更新上传队列配置
    this.registerHandler(
      'updateQueueConfig',
      async config => {
        if (!config) {
          throw new IPCHandlerError('队列配置不能为空')
        }
        return this.uploadTaskService.updateQueueConfig(config)
      }
    )

    // 获取当前上传队列状态
    this.registerHandler(
      'getQueueStatus',
      async () => {
        return this.uploadTaskService.getQueueStatus()
      }
    )

    // 清理已完成的任务
    this.registerHandler(
      'cleanupCompleted',
      async data => {
        if (!data) {
          throw new IPCHandlerError('清理已完成任务参数不能为空')
        }
        if (!data.uid) {
          throw new IPCHandlerError('用户ID不能为空')
        }
        return this.uploadTaskService.cleanupCompleted(data.uid, data.teamId, data.olderThanDays)
      }
    )

    // 上传文件内容
    this.registerHandler(
      'uploadFileContent',
      async data => {
        if (!data) {
          throw new IPCHandlerError('上传文件内容参数不能为空')
        }
        if (!data.taskId) {
          throw new IPCHandlerError('任务ID不能为空')
        }
        if (!data.fileContent) {
          throw new IPCHandlerError('文件内容不能为空')
        }
        return this.uploadTaskService.uploadFileContent(data.taskId, data.fileContent, data.fileName)
      }
    )

    // 选择文件
    this.registerHandler( 'selectFiles', data => this.uploadTaskService.selectFiles(data?.multiple, data?.filters))

    // 从本地路径上传文件
    this.registerHandler(
      'uploadFromPath',
      async data => {
        if (!data) {
          throw new IPCHandlerError('从路径上传文件参数不能为空')
        }
        if (!data.taskId) {
          throw new IPCHandlerError('任务ID不能为空')
        }
        if (!data.filePath) {
          throw new IPCHandlerError('文件路径不能为空')
        }
        return this.uploadTaskService.uploadFromPath(data.taskId, data.filePath)
      }
    )
  }
}

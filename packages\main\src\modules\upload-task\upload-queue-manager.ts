import { Injectable, Inject } from '@nestjs/common'
import { BrowserWindow } from 'electron'
import { readFileSync, statSync } from 'fs'
import { createHash } from 'crypto'
import { UploadTaskModel, UploadTaskStatus } from '@/infra/models/TaskModel.js'
import { UploadTaskService } from './upload-task.service.js'
import { FileUploaderService } from '../file-uploader/file-uploader.service.js'
import { UploadProgressEvent } from '@app/shared/types/upload-task.types.js'

/**
 * 上传队列管理器
 * 负责管理并发上传任务和队列调度
 */
@Injectable()
export class UploadQueueManager {

  /**
   * 当前活跃的上传任务
   */
  private activeUploads = new Map<number, AbortController>()

  /**
   * 上传进度定时器
   */
  private progressTimers = new Map<number, NodeJS.Timeout>()

  /**
   * 队列处理器定时器
   */
  private queueProcessorTimer?: NodeJS.Timeout

  /**
   * 是否已初始化
   */
  private initialized = false

  constructor(
    @Inject(UploadTaskService)
    private readonly uploadTaskService: UploadTaskService,
    @Inject(FileUploaderService)
    private readonly fileUploaderService: FileUploaderService
  ) {}

  /**
   * 启动队列处理器（公共方法）
   */
  startQueueProcessor(): void {
    if (this.initialized) {
      console.log('[UploadQueueManager] 队列处理器已经启动')
      return
    }

    console.log('[UploadQueueManager] 启动队列处理器')

    // 每5秒检查一次队列
    this.queueProcessorTimer = setInterval(() => {
      this.processQueue()
    }, 5000)

    this.initialized = true

    // 立即处理一次
    this.processQueue()
  }

  /**
   * 处理上传队列
   */
  private async processQueue(): Promise<void> {
    try {
      const config = this.uploadTaskService.getQueueConfig()
      const currentActiveCount = this.activeUploads.size

      // 如果已达到最大并发数，不处理新任务
      if (currentActiveCount >= config.max_concurrent_uploads) {
        return
      }

      // 获取等待上传的任务
      const pendingTasks = this.uploadTaskService.getTasksByStatus(UploadTaskStatus.PENDING)
      const availableSlots = config.max_concurrent_uploads - currentActiveCount

      // 取前N个任务开始上传
      const tasksToStart = pendingTasks.slice(0, availableSlots)

      for (const task of tasksToStart) {
        await this.startUpload(task)
      }
    } catch (error) {
      if (error instanceof Error && error.message.includes('数据库尚未初始化')) {
        return
      }
      console.error('[UploadQueueManager] 处理队列时出错:', error)
    }
  }

  /**
   * 开始上传任务
   */
  private async startUpload(task: UploadTaskModel): Promise<void> {
    try {
      // 检查任务是否已经在上传中
      if (this.activeUploads.has(task.id)) {
        console.log(`[UploadQueueManager] 任务 ${task.id} 已在上传中`)
        return
      }

      // 更新任务状态为上传中
      this.uploadTaskService.updateTaskStatus(task.id, UploadTaskStatus.UPLOADING)

      // 创建取消控制器
      const abortController = new AbortController()
      this.activeUploads.set(task.id, abortController)

      // 开始上传
      await this.performUpload(task, abortController)
    } catch (error) {
      console.error(`[UploadQueueManager] 开始上传任务 ${task.id} 时出错:`, error)
      this.uploadTaskService.updateTaskStatus(task.id, UploadTaskStatus.FAILED, error instanceof Error ? error.message : '上传失败')
      this.activeUploads.delete(task.id)
    }
  }

  /**
   * 执行上传
   */
  private async performUpload(task: UploadTaskModel, abortController: AbortController): Promise<void> {
    try {
      console.log(`[UploadQueueManager] 开始上传任务 ${task.id}: ${task.name}`)

      // 检查文件是否存在
      if (!this.fileExists(task.local_path)) {
        this.uploadTaskService.updateTaskStatus(task.id, UploadTaskStatus.FAILED, '文件不存在或无法访问')
        return
      }

      // 读取文件内容
      const fileBuffer = readFileSync(task.local_path)

      // 更新文件大小（如果没有的话）
      if (!task.size || task.size === 0) {
        task.size = fileBuffer.length
        this.uploadTaskService.update(task.id, { size: task.size })
      }

      // 计算文件哈希（如果没有的话）
      if (!task.hash) {
        const hash = createHash('md5').update(fileBuffer).digest('hex')
        task.hash = hash
        this.uploadTaskService.update(task.id, { hash })
      }

      // 设置进度回调
      const uploadId = `upload_${task.id}_${Date.now()}`

      // 启动进度监控
      this.startProgressMonitoring(task, uploadId)

      // 调用文件上传服务（启用断点续传）
      const result = await this.fileUploaderService.uploadBufferToOSS({
        buffer: fileBuffer,
        fileName: task.name,
        uploadId,
        folderUuid: task.folder_id || undefined,
        fileMd5: task.hash,
        module: task.upload_module as any,
        taskId: task.id,
        enableResume: true,
        partSize: 1024 * 1024 // 1MB 分片
      })

      // 停止进度监控
      this.stopProgressMonitoring(task.id)

      if (result.success) {
        // 上传成功，清除 checkpoint 数据
        this.uploadTaskService.update(task.id, {
          url: result.url!,
          object_id: result.objectId || '',
          object_key: result.fileName || '',
          progress: 100,
          uploaded_bytes: task.size,
          status: UploadTaskStatus.COMPLETED,
          checkpoint_data: '' // 清除断点数据
        })

        this.sendProgressEvent({
          task_id: task.id,
          uploaded_bytes: task.size,
          progress: 100
        })

        console.log(`[UploadQueueManager] 任务 ${task.id} 上传成功: ${result.url}`)
      } else {
        // 上传失败，保留 checkpoint 数据以便重试
        this.uploadTaskService.updateTaskStatus(task.id, UploadTaskStatus.FAILED, result.error || '上传失败')
        console.error(`[UploadQueueManager] 任务 ${task.id} 上传失败:`, result.error)
      }
    } catch (error) {
      console.error(`[UploadQueueManager] 执行上传任务 ${task.id} 时出错:`, error)
      this.uploadTaskService.updateTaskStatus(task.id, UploadTaskStatus.FAILED, error instanceof Error ? error.message : '上传异常')
    } finally {
      // 清理资源
      this.activeUploads.delete(task.id)
      this.stopProgressMonitoring(task.id)
    }
  }

  /**
   * 停止进度监控
   */
  private stopProgressMonitoring(taskId: number): void {
    const timer = this.progressTimers.get(taskId)
    if (timer) {
      clearInterval(timer)
      this.progressTimers.delete(taskId)
    }
  }

  /**
   * 启动进度监控
   */
  private startProgressMonitoring(task: UploadTaskModel, uploadId: string): void {
    const timer = setInterval(() => {
      // 这里可以实现更精确的进度监控
      // 目前依赖 FileUploaderService 的进度回调
    }, 1000)

    this.progressTimers.set(task.id, timer)
  }

  /**
   * 发送进度事件到渲染进程
   */
  private sendProgressEvent(event: UploadProgressEvent): void {
    const allWindows = BrowserWindow.getAllWindows()
    allWindows.forEach(window => {
      window.webContents.send('upload-task-progress', event)
    })
  }

  /**
   * 处理上传进度事件（包含 checkpoint 数据）
   */
  private handleUploadProgress(taskId: number, progress: number, uploadedBytes: number, checkpoint?: any): void {
    // 更新任务进度
    this.uploadTaskService.update(taskId, {
      progress: Math.round(progress),
      uploaded_bytes: uploadedBytes,
      // 保存 checkpoint 数据
      ...(checkpoint ? { checkpoint_data: JSON.stringify(checkpoint) } : {})
    })

    // 发送进度事件
    this.sendProgressEvent({
      task_id: taskId,
      uploaded_bytes: uploadedBytes,
      progress: Math.round(progress)
    })
  }

  /**
   * 检查文件是否存在
   */
  private fileExists(filePath: string): boolean {
    try {
      const stats = statSync(filePath)
      return stats.isFile()
    } catch {
      return false
    }
  }

  /**
   * 获取任务的 checkpoint 数据
   */
  private getTaskCheckpoint(task: UploadTaskModel): any | null {
    try {
      if (task.checkpoint_data) {
        return JSON.parse(task.checkpoint_data)
      }
      return null
    } catch (error) {
      console.error('[UploadQueueManager] 解析 checkpoint 数据失败:', error)
      return null
    }
  }

  /**
   * 清除任务的 checkpoint 数据
   */
  private clearTaskCheckpoint(taskId: number): void {
    this.uploadTaskService.update(taskId, {
      checkpoint_data: ''
    })
  }

  /**
   * 手动启动指定任务的上传
   */
  async startTaskUpload(taskId: number): Promise<boolean> {
    try {
      const task = this.uploadTaskService.findById(taskId)
      if (!task) {
        console.error(`[UploadQueueManager] 任务 ${taskId} 不存在`)
        return false
      }

      if (!task.isPending() && !task.isPaused() && !task.isFailed()) {
        console.error(`[UploadQueueManager] 任务 ${taskId} 当前状态不允许开始上传`)
        return false
      }

      // 启动上传
      await this.startUpload(task)
      return true
    } catch (error) {
      console.error(`[UploadQueueManager] 启动任务 ${taskId} 上传失败:`, error)
      return false
    }
  }

  /**
   * 暂停上传任务
   */
  pauseUpload(taskId: number): boolean {
    const abortController = this.activeUploads.get(taskId)
    if (abortController) {
      abortController.abort()
      this.activeUploads.delete(taskId)
      this.stopProgressMonitoring(taskId)
      return this.uploadTaskService.pauseTask(taskId)
    }
    return false
  }

  /**
   * 取消上传任务
   */
  cancelUpload(taskId: number): boolean {
    const abortController = this.activeUploads.get(taskId)
    if (abortController) {
      abortController.abort()
      this.activeUploads.delete(taskId)
      this.stopProgressMonitoring(taskId)
    }
    return this.uploadTaskService.cancelTask(taskId)
  }

  /**
   * 获取当前队列状态
   */
  getQueueStatus() {
    return {
      ...this.uploadTaskService.getQueueStatus(),
      active_uploads: Array.from(this.activeUploads.keys())
    }
  }

  /**
   * 清理资源（应用关闭时调用）
   */
  cleanup(): void {
    // 停止队列处理器
    if (this.queueProcessorTimer) {
      clearInterval(this.queueProcessorTimer)
      this.queueProcessorTimer = undefined
    }

    // 取消所有活跃的上传
    for (const [taskId, abortController] of this.activeUploads) {
      abortController.abort()
      try {
        this.uploadTaskService.updateTaskStatus(taskId, UploadTaskStatus.PAUSED, '应用关闭')
      } catch (error) {
        // 忽略清理时的错误
      }
    }

    // 清理定时器
    for (const timer of this.progressTimers.values()) {
      clearInterval(timer)
    }

    this.activeUploads.clear()
    this.progressTimers.clear()
    this.initialized = false
  }
}
